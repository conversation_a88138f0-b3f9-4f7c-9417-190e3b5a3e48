{"actions": [], "allow_rename": 1, "creation": "2025-04-20 23:08:20.488482", "doctype": "DocType", "engine": "InnoDB", "field_order": ["basic_info_section", "interface_code", "reference_document", "user", "column_break_bpng", "status", "return_code", "return_message", "request_response_section", "request_content", "response_content", "full_data_section", "request", "response"], "fields": [{"fieldname": "basic_info_section", "fieldtype": "Section Break", "label": "Basic Information"}, {"fieldname": "reference_document", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Reference Document"}, {"fieldname": "interface_code", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Interface Code"}, {"fieldname": "user", "fieldtype": "Link", "in_standard_filter": 1, "label": "User", "options": "User"}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "options": "Success\nError"}, {"fieldname": "return_code", "fieldtype": "Data", "in_list_view": 1, "label": "Return Code"}, {"fieldname": "return_message", "fieldtype": "Data", "label": "Return Message"}, {"fieldname": "request_response_section", "fieldtype": "Section Break", "label": "Request & Response Content"}, {"description": "The actual content being sent to EFRIS", "fieldname": "request_content", "fieldtype": "Code", "label": "Request Content"}, {"description": "The actual content received from EFRIS", "fieldname": "response_content", "fieldtype": "Code", "label": "Response Content"}, {"collapsible": 1, "fieldname": "full_data_section", "fieldtype": "Section Break", "label": "Full Request & Response Data"}, {"description": "Complete request payload including headers and metadata", "fieldname": "request", "fieldtype": "Code", "label": "Full Request"}, {"description": "Complete response including headers and metadata", "fieldname": "response", "fieldtype": "Code", "label": "Full Response"}, {"fieldname": "column_break_bpng", "fieldtype": "Column Break"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-04-21 10:51:27.446365", "modified_by": "Administrator", "module": "<PERSON><PERSON>", "name": "EFRIS Log", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}